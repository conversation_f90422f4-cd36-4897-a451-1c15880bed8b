'use client';
import { useQuery } from '@apollo/client';
import { useParams } from 'next/navigation';
import { ApplicationSubmission } from './components';
import GetApplicationSubmission from './components/ApplicationSubmission/GetApplicationSubmission.graphql';
import { Callout, Flex, SegmentedControl, Spinner } from '@radix-ui/themes';
import { ExclamationTriangleIcon, InfoCircledIcon } from '@radix-ui/react-icons';
import { useState } from 'react';
import GetCaseDetail from '../../pageQuery.graphql';
import { Application } from '@bybeam/platform-types';
import { hasMultiParty } from '@/spa-legacy/common/utilities/applicantType';

export default function CaseApplicationPage() {
  const { applicationId: initialApplicationId, caseId } = useParams<{
    applicationId: string;
    caseId: string;
  }>();
  const [applicationId, setApplicationId] = useState(initialApplicationId);

  const { data: caseData } = useQuery(GetCaseDetail, {
    variables: {
      id: caseId,
      includeRecentLogin: false,
    },
  });

  const program = caseData?.cases?.cases?.[0]?.program;
  const isMultiParty = program && hasMultiParty(program);
  const applications: Application[] = caseData?.cases?.cases?.[0]?.applications || [];
  console.log({ isMultiParty });

  return (
    <Flex direction="column" gap="2" align="center" width="100%">
      {isMultiParty && (
        <SegmentedControl.Root defaultValue="inbox" onValueChange={setApplicationId}>
          {applications?.map((application) => {
            const applicantType = application.submitter.applicantProfile?.applicantType;

            const programApplicantType = program.applicantTypes?.find((type) => {
              return type.applicantType.id === applicantType?.id;
            });

            return (
              <SegmentedControl.Item key={application.id} value={application.id}>
                {programApplicantType?.nameOverride || applicantType?.name}
              </SegmentedControl.Item>
            );
          })}
        </SegmentedControl.Root>
      )}
      <ApplicationSubmissionContainer applicationId={applicationId} />
    </Flex>
  );
}

function ApplicationSubmissionContainer({ applicationId }: { applicationId: string }) {
  const { data, loading, error } = useQuery(GetApplicationSubmission, {
    variables: {
      applicationSubmissionId: applicationId,
    },
  });

  if (error) {
    return (
      <Callout.Root color="red">
        <Callout.Icon>
          <ExclamationTriangleIcon />
        </Callout.Icon>
        <Callout.Text>
          Failed to load application. If this problem persists, please contact support.
        </Callout.Text>
      </Callout.Root>
    );
  }

  if (loading) {
    return <Spinner />;
  }

  if (!data?.applicationSubmission) {
    return (
      <Callout.Root color="yellow">
        <Callout.Icon>
          <InfoCircledIcon />
        </Callout.Icon>
        <Callout.Text>Application not found</Callout.Text>
      </Callout.Root>
    );
  }

  return <ApplicationSubmission applicationSubmission={data.applicationSubmission} />;
}
