import { REVIEW_STATUS_ICONS, REVIEW_STATUS_VALUES } from '@/app/components/features/FieldReview';
import CopyToClipboard from '@/app/components/ui/CopyToClipboard/CopyToClipboard';
import { formatAddress } from '@/spa-legacy/common/utilities/format';
import { useMutation } from '@apollo/client';
import {
  Address,
  Application,
  ApplicationAnswer,
  ApplicationAnswerReview,
  ApplicationAnswerReviewStatus,
  Document,
  Field,
  FieldType,
  ApplicationSubmission,
} from '@bybeam/platform-types';
import * as Accordion from '@radix-ui/react-accordion';
import {
  CardStackIcon,
  ChatBubbleIcon,
  ChevronRightIcon,
  CrossCircledIcon,
  EnterFullScreenIcon,
  InfoCircledIcon,
} from '@radix-ui/react-icons';
import {
  Box,
  Button,
  DataList,
  Dialog,
  Flex,
  Heading,
  HoverCard,
  IconButton,
  Inset,
  Popover,
  Select,
  Separator,
  Table,
  Text,
  TextArea,
  Tooltip,
} from '@radix-ui/themes';
import { <PERSON>, FieldValues, SubmitHandler, useForm } from 'react-hook-form';
import AddNoteToApplicationAnswer from './AddNoteToApplicationAnswer.graphql';
import AddReviewToApplicationAnswer from './AddReviewToApplicationAnswer.graphql';
import styles from './ApplicationSubmission.module.css';

const formatQuestionName = (key: string) => {
  return key.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, (str) => str.toUpperCase());
};

interface FieldResponse {
  field: Field;
  answer: ApplicationAnswer;
}
interface QuestionResponse {
  key: string;
  displayName?: string;
  name: string;
  fields: FieldResponse[];
}

interface QuestionGroupResponse {
  key: string;
  name: string;
  questions: QuestionResponse[];
}

interface SectionResponseProps {
  key: string;
  name: string;
  questionGroups: QuestionGroupResponse[];
}

type AddNoteInputs = {
  content: string;
  reviewStatus: ApplicationAnswerReviewStatus;
};

enum DocumentType {
  Image = 'image',
  PDF = 'pdf',
  CSV = 'csv',
  Other = 'other',
}

const getDocumentType = (mimetype: string): DocumentType => {
  if (mimetype.match('image.*')) return DocumentType.Image;
  if (mimetype?.match('pdf')) return DocumentType.PDF;
  if (mimetype?.match('csv')) return DocumentType.CSV;
  return DocumentType.Other;
};

const formatKey = (key: string) => {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/\.|(^.)/g, (str, offset) => {
      if (str === '.') {
        return ' > ';
      }
      return offset === 0 ? str.toUpperCase() : str;
    })
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

const cropFilename = (filename: string) => {
  return filename.length > 20 ? `${filename.slice(0, 10)}[...]${filename.slice(-7)}` : filename;
};

const formatTagName = (name: string) => {
  return name.replace(/_/g, ' ');
};

const AnswerReviewNote = ({ review }: { review: ApplicationAnswerReview }) => {
  return (
    <Flex key={review.id} asChild direction="column" className={styles.applicationAnswerNote} p="2">
      <li>
        <Flex gap="2" pl="1" align="center">
          {review.reviewStatus ? (
            <Tooltip key={review.id} content={REVIEW_STATUS_VALUES[review.reviewStatus]}>
              <span className={styles.reviewItemStatusIcon}>
                {REVIEW_STATUS_ICONS[review.reviewStatus]}
              </span>
            </Tooltip>
          ) : null}
          <Flex direction="column">
            <Text size="1">
              {review.reviewStatus ? REVIEW_STATUS_VALUES[review.reviewStatus] : null}
            </Text>
            <Flex gap="1">
              <Text size="1" color="gray">
                Reviewed by <strong>{review.note?.author?.identityUser?.name}</strong>
              </Text>
              <Text size="1" color="gray">
                on {new Date(review.createdAt).toLocaleString()}
              </Text>
            </Flex>
          </Flex>
        </Flex>
        {review.note?.content ? (
          <Box p="1" mt="2" className={styles.reviewNote}>
            <Text color="gray" size="2">
              {review.note?.content}
            </Text>
          </Box>
        ) : null}
      </li>
    </Flex>
  );
};

const ApplicationAnswerActions = ({
  fieldResponse,
  previousResponses,
  documents,
}: {
  fieldResponse: FieldResponse;
  previousResponses?: FieldResponse[];
  documents: Document[];
}) => {
  const [addNoteToApplicationAnswer] = useMutation(AddNoteToApplicationAnswer);
  const [addReviewToApplicationAnswer] = useMutation(AddReviewToApplicationAnswer);

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors, isValid },
  } = useForm<AddNoteInputs>({ mode: 'onTouched', reValidateMode: 'onChange' });

  const handleAddNote: SubmitHandler<AddNoteInputs> = async (formSubmission: FieldValues) => {
    const applicationAnswerId = fieldResponse.answer?.id;

    if (!applicationAnswerId) {
      return;
    }

    if (formSubmission.reviewStatus) {
      await addReviewToApplicationAnswer({
        variables: {
          input: {
            applicationAnswerId,
            content: formSubmission.content,
            reviewStatus: formSubmission.reviewStatus,
          },
        },
      });
    } else {
      await addNoteToApplicationAnswer({
        variables: {
          input: {
            applicationAnswerId,
            content: formSubmission.content,
          },
        },
      });
    }

    reset();
  };

  const previousReviewCount =
    previousResponses?.flatMap(({ answer }) => answer?.fieldReviews || []).length || 0;
  const currentReviewCount = fieldResponse.answer?.fieldReviews?.length || 0;
  const displayName = fieldResponse?.field?.displayName || formatKey(fieldResponse.field.key);
  return (
    <Flex gap="3" align="center" justify="end" px="1">
      <Flex gap="1">
        {fieldResponse.answer?.fieldReviews
          ?.filter(({ reviewStatus }) => reviewStatus !== ApplicationAnswerReviewStatus.NOTE_ONLY)
          .map((review) => {
            return review.reviewStatus ? (
              <Tooltip key={review.id} content={REVIEW_STATUS_VALUES[review.reviewStatus]}>
                <div>{REVIEW_STATUS_ICONS[review.reviewStatus]}</div>
              </Tooltip>
            ) : null;
          })}
      </Flex>
      <Popover.Root>
        <Popover.Trigger>
          <Button variant="ghost" color="blue" aria-label={`Add review for ${displayName}`}>
            <ChatBubbleIcon /> {previousReviewCount + currentReviewCount || ''}
          </Button>
        </Popover.Trigger>
        <Popover.Content align="end" side="left" width="420px" maxHeight={'90vh'}>
          <Inset>
            <Flex
              direction="column"
              align="end"
              width="100%"
              className={styles.applicationAnswerPopoverContent}
            >
              {previousResponses?.map((response) => {
                return response.answer?.fieldReviews?.length ? (
                  <Box
                    key={response.field.key}
                    width="100%"
                    className={styles.previousReviewsNotes}
                  >
                    <Flex direction="column" px="4" py="2">
                      <Text weight="medium" size="2">
                        Reviews for previous response
                      </Text>
                      <Text size="2">
                        <FieldResponseValue fieldResponse={response} documents={documents} />
                      </Text>
                    </Flex>
                    <Flex asChild direction="column" gap="1" width="100%" px="4" pb="4">
                      <ul>
                        {response.answer?.fieldReviews?.map((review) => {
                          return <AnswerReviewNote key={review.id} review={review} />;
                        })}
                      </ul>
                    </Flex>
                    <Separator size="4" />
                  </Box>
                ) : null;
              })}
              {fieldResponse.answer?.fieldReviews?.length ? (
                <Flex asChild direction="column" gap="1" width="100%" px="4" pt="4">
                  <ul>
                    {fieldResponse.answer?.fieldReviews?.map((review) => {
                      return <AnswerReviewNote key={review.id} review={review} />;
                    })}
                  </ul>
                </Flex>
              ) : null}
              <Box asChild width="100%" px="4" pt="2" pb="2">
                <form onSubmit={handleSubmit(handleAddNote)}>
                  <Flex direction="column" gap="2" align="end">
                    <Flex direction="column" width="100%">
                      <Flex direction="column" mb="1">
                        <Text
                          as="label"
                          id={`${fieldResponse.field.key}-review-content`}
                          size="2"
                          className={styles.textAreaLabel}
                        >
                          Add new review for "{displayName}"
                        </Text>
                      </Flex>
                      <TextArea
                        autoFocus
                        aria-labelledby={`${fieldResponse.field.key}-review-content`}
                        placeholder="Write a note..."
                        className={styles.NoteTextArea}
                        {...register('content')}
                      />
                      <Text size="1" color="gray">
                        Review notes are never visible to the applicant.
                      </Text>
                    </Flex>
                    <Flex width="100%" justify="between" gap="2">
                      <Flex gap="2" align="center">
                        <Controller
                          name="reviewStatus"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <Select.Root size="2" onValueChange={field.onChange}>
                              <Select.Trigger placeholder="Review status (required)" />
                              <Select.Content>
                                <Select.Group>
                                  <Select.Label>Review status</Select.Label>
                                  {Object.entries(REVIEW_STATUS_VALUES).map(([key, value]) => (
                                    <Select.Item key={key} value={key}>
                                      <Flex gap="2" align="center">
                                        {REVIEW_STATUS_ICONS[key as ApplicationAnswerReviewStatus]}{' '}
                                        {value}
                                      </Flex>
                                    </Select.Item>
                                  ))}
                                </Select.Group>
                              </Select.Content>
                            </Select.Root>
                          )}
                        />
                        <Tooltip
                          maxWidth="240px"
                          content="Applicant will only be informed of the review status for a given field if it requires resubmission AND the case is marked incomplete."
                        >
                          <InfoCircledIcon tabIndex={0} />
                        </Tooltip>
                      </Flex>
                      <Popover.Close disabled={!isValid}>
                        <Button size="2" type="submit">
                          Submit review
                        </Button>
                      </Popover.Close>
                    </Flex>
                  </Flex>
                </form>
              </Box>
            </Flex>
          </Inset>
        </Popover.Content>
      </Popover.Root>
    </Flex>
  );
};

// TODO: Resolve documents in API so we don't have to stitch together like this
const FieldResponseValue = ({
  fieldResponse,
  documents,
  addresses,
}: { fieldResponse: FieldResponse; documents: Document[]; addresses?: Address[] }) => {
  const document = documents.find(({ id }) => id === fieldResponse.answer?.value);

  if (!fieldResponse.answer?.value) {
    return <Text color="gold">Not answered</Text>;
  }

  switch (fieldResponse.field.type) {
    case FieldType.Document:
      return document ? (
        <DocumentPreview document={document} fieldResponse={fieldResponse} />
      ) : (
        <></>
      );
    case FieldType.Address: {
      const address = addresses?.find(({ id }) => id === fieldResponse.answer?.value);
      return address ? JSON.stringify(formatAddress(address)) : <></>;
    }
    case FieldType.Complex:
      return <ComplexResponsePreview fieldResponse={fieldResponse} />;
    default:
      return JSON.stringify(fieldResponse.answer?.value, null, 2);
  }
};

const DocumentPreview = ({
  document,
  fieldResponse,
}: { document: Document; fieldResponse: FieldResponse }) => {
  const displayName = fieldResponse?.field?.displayName || fieldResponse.field.key;
  return (
    <Dialog.Root>
      <HoverCard.Root>
        <HoverCard.Trigger>
          <Dialog.Trigger>
            <button type="button">
              <Flex gap="1" align="center">
                <Box height="2rem" width="2rem">
                  {getDocumentType(document.mimetype) === DocumentType.Image ? (
                    <img
                      title={document.filename}
                      src={document.previewUrl}
                      className={styles.documentPreview}
                      alt={`Preview of document upload for ${displayName}`}
                    />
                  ) : getDocumentType(document.mimetype) === DocumentType.PDF ? (
                    <object
                      data={`${document.previewUrl}`}
                      className={styles.documentPreview}
                      aria-label={`Preview document upload for ${displayName}`}
                    />
                  ) : null}
                </Box>
                {cropFilename(document.filename)}
              </Flex>
            </button>
          </Dialog.Trigger>
        </HoverCard.Trigger>
        <HoverCard.Content minWidth="240px" maxWidth="360px" sideOffset={4} alignOffset={16}>
          <Inset>
            <Flex direction="column">
              <Flex p="2" justify="between" gap="2">
                <Text size="1">{document.filename}</Text>
                <Dialog.Trigger>
                  <IconButton
                    variant="ghost"
                    aria-label="View full screen"
                    color="gray"
                    className={styles.fullScreenButton}
                  >
                    <EnterFullScreenIcon />
                  </IconButton>
                </Dialog.Trigger>
              </Flex>
              <Flex
                align="center"
                justify="center"
                minHeight="8rem"
                className={styles.documentHoverImageContainer}
              >
                {getDocumentType(document.mimetype) === DocumentType.Image ? (
                  <img
                    title={document.filename}
                    src={document.previewUrl}
                    className={styles.document}
                    alt={`Document upload for ${displayName}`}
                  />
                ) : getDocumentType(document.mimetype) === DocumentType.PDF ? (
                  <iframe
                    src={document.previewUrl}
                    title={`Document upload for ${displayName}`}
                    className={styles.documentHoverImage}
                  />
                ) : null}
              </Flex>
              <Box p="2">
                <Flex asChild gapX="2" gapY="1" wrap="wrap">
                  <ul>
                    {document.documentTags?.map(({ tag }) => {
                      return (
                        <Flex asChild key={tag.id}>
                          <li>
                            <Tooltip content={tag.description}>
                              <Text size="1" color="gold" className={styles.documentTag}>
                                {formatTagName(tag.name)}
                              </Text>
                            </Tooltip>
                          </li>
                        </Flex>
                      );
                    })}
                  </ul>
                </Flex>
              </Box>
            </Flex>
          </Inset>
        </HoverCard.Content>
      </HoverCard.Root>
      <Dialog.Content size="1" maxWidth="90vw" height="90vh" maxHeight="90vh">
        <Flex direction="column" height="100%" width="100%" minHeight="0">
          <Flex width="100%" justify="end">
            <Dialog.Close>
              <IconButton variant="soft" size="2" className={styles.fullScreenCloseButton}>
                <CrossCircledIcon height="24" width="24" />
              </IconButton>
            </Dialog.Close>
          </Flex>
          {getDocumentType(document.mimetype) === DocumentType.Image ? (
            <Flex
              width="100%"
              height="100%"
              justify="center"
              align="center"
              className={styles.fullScreenDocumentImage}
              minHeight="0"
            >
              <img
                title={document.filename}
                src={document.previewUrl}
                className={styles.fullScreenDocument}
                alt={`Document upload for ${displayName}`}
              />
            </Flex>
          ) : getDocumentType(document.mimetype) === DocumentType.PDF ? (
            <iframe
              src={document.previewUrl}
              className={styles.fullScreenDocumentPDF}
              title={`Document upload for ${displayName}`}
            />
          ) : null}
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

const ComplexResponsePreview = ({ fieldResponse }: { fieldResponse: FieldResponse }) => {
  const response: Record<string, string> = JSON.parse(fieldResponse.answer?.value);
  return (
    <Dialog.Root>
      <HoverCard.Root>
        <HoverCard.Trigger>
          <Dialog.Trigger>
            <Flex asChild gap="1" align="center">
              <button type="button">
                {Object.entries(response).shift()?.[1]} <CardStackIcon />
              </button>
            </Flex>
          </Dialog.Trigger>
        </HoverCard.Trigger>
        <HoverCard.Content sideOffset={4} alignOffset={16}>
          <DataList.Root>
            {Object.entries(response).map(([key, value], index) => {
              const displayNameText =
                'subFields' in fieldResponse.field &&
                fieldResponse.field?.subFields?.[index]?.displayName;
              const displayName = (
                <Flex align="center" gap="1">
                  <strong>
                    {displayNameText || formatKey(key.replace(`${fieldResponse.field.key}_`, ''))}
                  </strong>
                  {displayNameText && (
                    <Tooltip content={`Actual key value: ${fieldResponse.field.key}`}>
                      <IconButton
                        variant="ghost"
                        aria-label={`Actual key value: ${fieldResponse.field.key}`}
                      >
                        <InfoCircledIcon color="gray" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Flex>
              );
              return (
                <DataList.Item key={key}>
                  <DataList.Label>{displayName}</DataList.Label>
                  <DataList.Value>{value}</DataList.Value>
                </DataList.Item>
              );
            })}
          </DataList.Root>
        </HoverCard.Content>
      </HoverCard.Root>
      <Dialog.Content>
        <DataList.Root>
          {Object.entries(response).map(([key, value], index) => {
            const displayNameText =
              'subFields' in fieldResponse.field &&
              fieldResponse.field?.subFields?.[index]?.displayName;
            const displayName = (
              <Flex align="center" gap="1">
                <strong>
                  {displayNameText || formatKey(key.replace(`${fieldResponse.field.key}_`, ''))}
                </strong>
                {displayNameText && (
                  <Tooltip content={`Actual key value: ${fieldResponse.field.key}`}>
                    <IconButton
                      variant="ghost"
                      aria-label={`Actual key value: ${fieldResponse.field.key}`}
                    >
                      <InfoCircledIcon color="gray" />
                    </IconButton>
                  </Tooltip>
                )}
              </Flex>
            );
            return (
              <DataList.Item key={key}>
                <DataList.Label>{displayName}</DataList.Label>
                <DataList.Value>{value}</DataList.Value>
              </DataList.Item>
            );
          })}
        </DataList.Root>
      </Dialog.Content>
    </Dialog.Root>
  );
};

function SectionResponse({
  sectionResponse,
  applicationSubmission,
}: { sectionResponse: SectionResponseProps; applicationSubmission: Application }) {
  return (
    <Flex gap="4" direction="column">
      {sectionResponse?.questionGroups?.map((questionGroupResponse: QuestionGroupResponse) => {
        return (
          <div key={questionGroupResponse.key}>
            <Table.Root size="1" variant="surface">
              <Table.Header>
                <Table.ColumnHeaderCell width="30%">Question name</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell width="30%">Field</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell width="30%">Response</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Actions</Table.ColumnHeaderCell>
              </Table.Header>
              {questionGroupResponse.questions.map((questionResponse) => {
                return (
                  <Table.Body key={questionResponse.key}>
                    {questionResponse.fields.map((fieldResponse) => {
                      if (fieldResponse.field.type === 'typography') {
                        return <></>;
                      }

                      //
                      const previousResponses = applicationSubmission.versions
                        ?.flatMap((applicationVersion) => {
                          // TODO: Fix this type casting
                          const { mappedAnswers } = applicationVersion as unknown as {
                            mappedAnswers: SectionResponseProps[];
                          };
                          return mappedAnswers.flatMap((sectionResponse: SectionResponseProps) => {
                            return sectionResponse.questionGroups.flatMap(
                              (questionGroupResponse: QuestionGroupResponse) => {
                                return questionGroupResponse.questions.flatMap(
                                  (questionResponse) => {
                                    return questionResponse.fields.flat();
                                  },
                                );
                              },
                            );
                          });
                        })
                        .filter((previousResponse) => {
                          if (!previousResponse.answer && !fieldResponse.answer) {
                            return false;
                          }
                          return previousResponse.field?.key === fieldResponse.field?.key;
                        });
                      return (
                        <Table.Row
                          key={fieldResponse.field.key}
                          align={'center'}
                          className={styles.tableRow}
                        >
                          <Table.Cell>
                            <DisplayNameWithTooltip questionResponse={questionResponse} />
                          </Table.Cell>
                          <Table.Cell>
                            <DisplayNameWithTooltip fieldResponse={fieldResponse} />
                          </Table.Cell>
                          <Table.Cell>
                            <FieldResponseValue
                              fieldResponse={fieldResponse}
                              documents={applicationSubmission.documents || []}
                              addresses={applicationSubmission.addresses || []}
                            />
                          </Table.Cell>
                          <Table.Cell>
                            <ApplicationAnswerActions
                              fieldResponse={fieldResponse}
                              previousResponses={previousResponses}
                              documents={applicationSubmission.documents || []}
                            />
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                );
              })}
            </Table.Root>
          </div>
        );
      })}
    </Flex>
  );
}

function DisplayNameWithTooltip({
  fieldResponse,
  questionResponse,
}: { fieldResponse?: FieldResponse; questionResponse?: QuestionResponse }) {
  if (!fieldResponse && !questionResponse) return null;
  if (fieldResponse) {
    const displayName = fieldResponse.field?.displayName;
    return (
      <Flex align="center" gap="1">
        <strong>{displayName || formatKey(fieldResponse.field.key)} </strong>
        {displayName && (
          <Tooltip content={`Actual key value: ${fieldResponse.field.key}`}>
            <IconButton variant="ghost" aria-label={`Actual key value: ${fieldResponse.field.key}`}>
              <InfoCircledIcon color="gray" />
            </IconButton>
          </Tooltip>
        )}
      </Flex>
    );
  }
  if (questionResponse) {
    const displayName = questionResponse?.displayName;
    return (
      <Flex align="center" gap="1">
        {displayName || formatKey(questionResponse.key)}{' '}
        {displayName && (
          <Tooltip content={`Actual key value: ${questionResponse.key}`}>
            <IconButton variant="ghost" aria-label={`Actual key value: ${questionResponse.key}`}>
              <InfoCircledIcon color="gray" />
            </IconButton>
          </Tooltip>
        )}
      </Flex>
    );
  }
}

export default function ApplicationSubmissionPage({
  applicationSubmission,
}: { applicationSubmission: ApplicationSubmission }) {
  return (
    <>
      <Flex
        gapX="2"
        gapY="1"
        justify="between"
        px="4"
        py="1"
        align="center"
        wrap="wrap"
        className={styles.applicationHeading}
      >
        <CopyToClipboard.Root>
          <CopyToClipboard.Content>
            <Text size="2" title="Application ID" weight="bold">
              {applicationSubmission.displayId}
            </Text>
          </CopyToClipboard.Content>
          <CopyToClipboard.Trigger
            text={applicationSubmission.displayId}
            contentType="Application_Id"
          />
        </CopyToClipboard.Root>
        <Flex gap="2" align="center" justify="between">
          <Text size="1" title="Date created">
            📅 {new Date(applicationSubmission.createdAt).toLocaleString()}
          </Text>
          <Text size="1" title="Last updated">
            🕒 {new Date(applicationSubmission.updatedAt).toLocaleString()}
          </Text>
        </Flex>
      </Flex>
      <Flex asChild direction="column" px="4" py="4" width="100%">
        <Accordion.Root
          type="multiple"
          className={styles.applicationAccordion}
          defaultValue={applicationSubmission.currentVersion?.mappedAnswers.map(
            (sectionResponse: SectionResponseProps) => sectionResponse.key,
          )}
        >
          {applicationSubmission.currentVersion?.mappedAnswers.map(
            (sectionResponse: SectionResponseProps) => {
              return (
                <Accordion.Item
                  value={sectionResponse.key}
                  key={sectionResponse.key}
                  className={styles.applicationAccordionItem}
                >
                  <Flex
                    justify="between"
                    align="center"
                    px="4"
                    py="2"
                    width="100%"
                    asChild
                    className={styles.AccordionTrigger}
                  >
                    <Accordion.Trigger>
                      <Heading size="3" mb="1">
                        {formatQuestionName(sectionResponse.key)}
                      </Heading>
                      <ChevronRightIcon className={styles.AccordionIcon} />
                    </Accordion.Trigger>
                  </Flex>
                  <Accordion.Content className={styles.AccordionContent}>
                    <SectionResponse
                      sectionResponse={sectionResponse}
                      applicationSubmission={applicationSubmission}
                    />
                  </Accordion.Content>
                </Accordion.Item>
              );
            },
          )}
        </Accordion.Root>
      </Flex>
    </>
  );
}
